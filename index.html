<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MemesByKayé - Personal Meme Collection</title>
    <meta name="description" content="A personal collection of memes featuring <PERSON><PERSON> and friends. Discover hilarious moments and reactions in this curated meme gallery.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:title" content="MemesByKayé - Personal Meme Collection">
    <meta property="og:description" content="A personal collection of memes featuring <PERSON><PERSON> and friends. Discover hilarious moments and reactions in this curated meme gallery.">
    <meta property="og:image" content="">
    <meta property="og:site_name" content="MemesByKayé">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="">
    <meta name="twitter:title" content="MemesByKayé - Personal Meme Collection">
    <meta name="twitter:description" content="A personal collection of memes featuring <PERSON><PERSON> and friends. Discover hilarious moments and reactions in this curated meme gallery.">
    <meta name="twitter:image" content="">

    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3ClinearGradient id='grad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%234facfe;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%2300f2fe;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='32' height='32' rx='8' fill='url(%23grad)'/%3E%3Ccircle cx='16' cy='16' r='12' fill='none' stroke='rgba(255,255,255,0.2)' stroke-width='1'/%3E%3Ctext x='16' y='22' text-anchor='middle' fill='white' font-family='Inter, sans-serif' font-weight='bold' font-size='16'%3EK%3C/text%3E%3C/svg%3E">
    <link rel="stylesheet" href="/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Subtle background gradient -->
    <div class="bg-gradient"></div>

    <!-- Navigation Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <span>K</span>
                    </div>
                    <h1 class="logo-text">MemesByKayé</h1>
                </div>
                
                <a href="/admin" class="admin-btn">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <div class="status-dot"></div>
                    Personal meme collection
                </div>
                
                <h1 class="hero-title">
                    <span class="gradient-text-accent">Memes</span>
                    <span class="text-primary">By</span>
                    <span class="gradient-text-secondary">Kayé</span>
                </h1>
                
                <p class="hero-description">
                    Browse and discover curated meme collections organized like your favorite series.
                </p>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats" id="series">
        <div class="container">
            <div class="stats-header">
                <h2 class="stats-title">Collection Overview</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number gradient-text-accent" id="series-count">0</div>
                        <div class="stat-label">Collections</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number gradient-text-secondary" id="memes-count">0</div>
                        <div class="stat-label">Memes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number gradient-text-neon">100%</div>
                        <div class="stat-label">Quality</div>
                    </div>
                </div>
            </div>

            <!-- Collections Grid -->
            <div class="series-grid" id="series-grid">
                <!-- Collection cards will be dynamically loaded here -->
            </div>

            <!-- Empty State -->
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">
                    <svg class="icon-large" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                </div>

                <h3 class="empty-title">No meme collections yet</h3>
                <p class="empty-description">
                    Upload your first meme collection to get started.
                </p>

                <a href="/admin" class="cta-button">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create First Collection
                </a>
            </div>
        </div>
    </section>

    <!-- API Client -->
    <script src="/lib/api-client.js"></script>
    <!-- Main Application Script -->
    <script src="/script-serverless.js"></script>
</body>
</html>
