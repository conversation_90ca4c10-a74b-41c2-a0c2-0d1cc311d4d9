{"buildCommand": "echo 'No build needed for static site'", "outputDirectory": ".", "functions": {"api/collections.js": {"maxDuration": 10}, "api/upload.js": {"maxDuration": 30}, "api/auth.js": {"maxDuration": 5}}, "rewrites": [{"source": "/", "destination": "/index.html"}, {"source": "/home", "destination": "/index.html"}, {"source": "/admin", "destination": "/admin.html"}, {"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/([^/]+)$", "destination": "/series.html?collection=$1"}, {"source": "/([^/]+)/([^/]+)$", "destination": "/meme.html?collection=$1&meme=$2"}], "redirects": [{"source": "/index.html", "destination": "/home", "permanent": true}, {"source": "/admin.html", "destination": "/admin", "permanent": true}, {"source": "/series.html", "destination": "/home", "permanent": true}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}