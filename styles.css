/* Professional Design System */
:root {
  /* Professional dark theme */
  --bg-primary: #0a0a0f;
  --bg-secondary: #111118;
  --bg-tertiary: #1a1a24;
  --bg-glass: rgba(255, 255, 255, 0.03);
  --bg-glass-hover: rgba(255, 255, 255, 0.06);
  --bg-card: rgba(255, 255, 255, 0.04);
  --bg-card-hover: rgba(255, 255, 255, 0.08);
  
  /* Professional gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-neon: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  
  /* Brand colors */
  --electric-blue: #00d4ff;
  --electric-purple: #8b5cf6;
  --electric-pink: #f472b6;
  --electric-green: #10b981;
  
  /* Text hierarchy */
  --text-primary: #ffffff;
  --text-secondary: #a1a1aa;
  --text-muted: #71717a;
  --text-accent: #00d4ff;
  
  /* Borders */
  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.05);
  --border-accent: rgba(0, 212, 255, 0.3);
  
  /* Spacing scale */
  --space-1: 0.25rem; /* 4px */
  --space-2: 0.5rem;  /* 8px */
  --space-3: 0.75rem; /* 12px */
  --space-4: 1rem;    /* 16px */
  --space-5: 1.25rem; /* 20px */
  --space-6: 1.5rem;  /* 24px */
  --space-8: 2rem;    /* 32px */
  --space-10: 2.5rem; /* 40px */
  --space-12: 3rem;   /* 48px */
  --space-16: 4rem;   /* 64px */
  --space-20: 5rem;   /* 80px */
  --space-24: 6rem;   /* 96px */
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--bg-primary);
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  color: var(--text-primary);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--electric-blue);
  outline-offset: 2px;
}

/* Background Elements */
.bg-gradient {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom right, rgba(139, 92, 246, 0.02), transparent, rgba(0, 212, 255, 0.02));
  pointer-events: none;
}

/* Utility Classes */
.container {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}

.card {
  background: var(--bg-card);
  backdrop-filter: blur(16px);
  border: 1px solid var(--border-secondary);
  border-radius: 1rem;
  transition: all 0.2s ease;
}

.card:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-primary);
  transform: translateY(-2px);
}

.icon {
  width: 1rem;
  height: 1rem;
}

.icon-large {
  width: 2rem;
  height: 2rem;
}

/* Text Utilities */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.gradient-text-accent {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-neon {
  background: var(--gradient-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-fade-in { animation: fadeIn 0.8s ease-out; }
.animate-slide-up { animation: slideUp 0.6s ease-out; }
.animate-scale-in { animation: scaleIn 0.5s ease-out; }
.animate-float { animation: float 6s ease-in-out infinite; }
.animate-pulse { animation: pulse 2s ease-in-out infinite; }
.animate-spin { animation: spin 1s linear infinite; }

/* Header Styles */
.header {
  position: relative;
  z-index: 10;
  border-bottom: 1px solid var(--border-secondary);
  background: rgba(10, 10, 15, 0.8);
  backdrop-filter: blur(16px);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.logo-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--gradient-accent);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
  transition: all 0.3s ease;
}

.logo-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(79, 172, 254, 0.4);
}

.logo-icon span {
  color: white;
  font-weight: bold;
  font-size: 1.125rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.admin-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  min-height: 44px;
  transition: all 0.2s ease;
}

.admin-btn:hover {
  color: var(--text-primary);
  background: var(--bg-card-hover);
}

/* Hero Section */
.hero {
  position: relative;
  z-index: 10;
  padding: var(--space-16) 0 var(--space-12);
}

.hero-content {
  text-align: center;
  max-width: 64rem;
  margin: 0 auto;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  background: var(--bg-glass);
  border: 1px solid var(--border-secondary);
  border-radius: 9999px;
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-bottom: var(--space-6);
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: var(--electric-green);
  border-radius: 50%;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: var(--space-6);
  line-height: 1.2;
}

@media (min-width: 640px) {
  .hero-title { font-size: 3rem; }
}

@media (min-width: 1024px) {
  .hero-title { font-size: 3.75rem; }
}

.hero-description {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  max-width: 32rem;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Stats Section */
.stats {
  position: relative;
  z-index: 10;
  padding: var(--space-12) 0;
}

.stats-header {
  text-align: center;
  margin-bottom: var(--space-12);
}

.stats-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: var(--space-8);
}

@media (min-width: 640px) {
  .stats-title { font-size: 1.875rem; }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
  max-width: 32rem;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .stats-grid { gap: var(--space-16); }
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.875rem;
  font-weight: bold;
  margin-bottom: var(--space-2);
}

@media (min-width: 640px) {
  .stat-number { font-size: 2.25rem; }
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Series Grid */
.series-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

@media (min-width: 640px) {
  .series-grid { grid-template-columns: repeat(2, 1fr); }
}

@media (min-width: 1024px) {
  .series-grid { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 1280px) {
  .series-grid { grid-template-columns: repeat(4, 1fr); }
}

.series-card {
  position: relative;
  overflow: hidden;
  height: 100%;
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

.series-cover {
  position: relative;
  aspect-ratio: 3/4;
  overflow: hidden;
  background: var(--bg-secondary);
}

.series-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.series-card:hover .series-image {
  transform: scale(1.05);
}

.series-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
}

.series-badges {
  position: absolute;
  top: var(--space-3);
  left: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.series-badge {
  padding: var(--space-1) var(--space-2);
  backdrop-filter: blur(8px);
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.series-badge.k-badge {
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: var(--electric-blue);
}

.series-badge.count-badge {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  font-weight: 600;
  min-width: fit-content;
  white-space: nowrap;
}

.series-hover-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.series-card:hover .series-hover-overlay {
  opacity: 1;
}

.series-view-btn {
  padding: var(--space-2) var(--space-4);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.75rem;
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  transform: translateY(0.5rem);
  transition: transform 0.3s ease;
}

.series-card:hover .series-view-btn {
  transform: translateY(0);
}

.series-content {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.series-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  transition: color 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.series-card:hover .series-title {
  color: var(--electric-blue);
}

.series-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--space-3);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
}

.series-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--space-2);
  border-top: 1px solid var(--border-secondary);
  margin-top: auto;
}

.series-date {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-muted);
  font-size: 0.75rem;
}

.series-date-dot {
  width: 0.375rem;
  height: 0.375rem;
  background: var(--electric-green);
  border-radius: 50%;
}

.series-characters {
  display: flex;
  gap: var(--space-1);
}

.character-tag {
  padding: var(--space-1) var(--space-2);
  background: var(--bg-secondary);
  border: 1px solid var(--border-secondary);
  border-radius: 0.25rem;
  color: var(--text-secondary);
  font-size: 0.75rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-16) 0;
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto var(--space-6);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon svg {
  width: 2rem;
  height: 2rem;
  color: var(--text-muted);
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

@media (min-width: 768px) {
  .empty-title { font-size: 1.5rem; }
}

.empty-description {
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  max-width: 28rem;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: var(--gradient-accent);
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  text-decoration: none;
  min-height: 44px;
  transition: transform 0.2s ease;
}

.cta-button:hover {
  transform: scale(1.05);
}

/* Series Page Styles */
.series-header {
  position: sticky;
  top: 0;
  z-index: 50;
  background: rgba(10, 10, 15, 0.8);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid var(--border-secondary);
}

.series-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  min-height: 44px;
  padding: 0 var(--space-2);
  transition: color 0.2s ease;
}

.back-btn:hover {
  color: var(--text-primary);
}

.view-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.view-toggle-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  min-height: 44px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-toggle-btn:hover {
  color: var(--text-primary);
  background: var(--bg-card-hover);
}

.series-container {
  padding: var(--space-8) 0;
}

.series-info {
  margin-bottom: var(--space-8);
}

.memes-section {
  margin-top: var(--space-8);
}

.memes-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}

.memes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--space-6);
}

.meme-card {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.meme-image-container {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  background: var(--bg-secondary);
}

.meme-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.meme-card:hover .meme-image {
  transform: scale(1.05);
}

.meme-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.meme-card:hover .meme-overlay {
  opacity: 1;
}

.meme-play-btn {
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.meme-info {
  padding: var(--space-4);
}

.meme-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.meme-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
}

.modal-container {
  position: relative;
  width: 100%;
  max-width: 64rem;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-secondary);
  background: rgba(17, 17, 24, 0.5);
  backdrop-filter: blur(8px);
}

.modal-title-section {
  min-width: 0;
  flex: 1;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.modal-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-top: var(--space-1);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.modal-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-left: var(--space-4);
}

.modal-action-btn {
  padding: var(--space-2);
  color: var(--text-secondary);
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  min-height: 44px;
  min-width: 44px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-action-btn:hover {
  color: var(--text-primary);
  background: var(--bg-card);
}

.modal-content {
  display: flex;
  flex-direction: column;
  max-height: calc(90vh - 80px);
}

@media (min-width: 1024px) {
  .modal-content {
    flex-direction: row;
  }
}

.modal-image-section {
  flex: 1;
  position: relative;
  background: var(--bg-secondary);
}

.modal-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.modal-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.modal-loading {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-secondary);
  border-top: 2px solid var(--electric-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.modal-sidebar {
  width: 100%;
  background: var(--bg-card);
  border-top: 1px solid var(--border-secondary);
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

@media (min-width: 1024px) {
  .modal-sidebar {
    width: 20rem;
    border-top: none;
    border-left: 1px solid var(--border-secondary);
  }
}

.modal-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.meta-label {
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
}

.meta-value {
  color: var(--text-primary);
  font-size: 0.875rem;
}

.meta-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-1);
}

.meta-tag {
  padding: var(--space-1) var(--space-2);
  background: var(--bg-secondary);
  border: 1px solid var(--border-secondary);
  border-radius: 0.25rem;
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.modal-actions-bottom {
  margin-top: auto;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--gradient-accent);
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  min-height: 44px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.action-button:hover {
  opacity: 0.9;
}

/* Admin Panel Styles */
.admin-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
}

.login-container {
  width: 100%;
  max-width: 28rem;
}

@media (max-width: 768px) {
  .admin-login {
    padding: var(--space-3);
    align-items: flex-start;
    padding-top: 20vh;
  }

  .login-container {
    max-width: 100%;
  }
}

.login-card {
  padding: var(--space-8);
}

.login-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.login-logo {
  width: 3.5rem;
  height: 3.5rem;
  margin: 0 auto var(--space-4);
  background: var(--gradient-accent);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.3);
}

.login-logo span {
  color: white;
  font-weight: bold;
  font-size: 1.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.login-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.login-description {
  color: var(--text-secondary);
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

@media (max-width: 768px) {
  .form-group {
    gap: var(--space-2);
  }
}

.form-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-primary);
  font-size: 1rem;
  min-height: 44px;
  transition: border-color 0.2s ease;
}

.form-input::placeholder {
  color: var(--text-muted);
}

.form-input:focus {
  border-color: var(--electric-blue);
}

.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-primary);
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.form-textarea::placeholder {
  color: var(--text-muted);
}

.form-textarea:focus {
  border-color: var(--electric-blue);
}

@media (max-width: 768px) {
  .form-input,
  .form-textarea {
    padding: var(--space-3);
    font-size: 1rem;
    border-radius: 0.5rem;
  }

  .form-textarea {
    min-height: 100px;
  }
}

/* File Upload Styles */
.file-upload-section {
  margin-bottom: var(--space-3);
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-upload-btn:hover {
  background: var(--bg-card-hover);
  border-color: var(--electric-blue);
  color: var(--text-primary);
}

.file-upload-btn .icon {
  width: 1rem;
  height: 1rem;
}

.file-name {
  display: inline-block;
  margin-left: var(--space-2);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-style: italic;
}

.form-divider {
  text-align: center;
  color: var(--text-muted);
  font-size: 0.75rem;
  margin: var(--space-2) 0;
  position: relative;
}

.form-divider::before,
.form-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 45%;
  height: 1px;
  background: var(--border-secondary);
}

.form-divider::before {
  left: 0;
}

.form-divider::after {
  right: 0;
}

/* Drag and Drop Styles */
.drag-drop-zone {
  border: 2px dashed var(--border-secondary);
  border-radius: 0.75rem;
  padding: var(--space-6);
  text-align: center;
  transition: all 0.3s ease;
  background: var(--bg-card);
  position: relative;
  min-height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drag-drop-zone.drag-over {
  border-color: var(--electric-blue);
  background: rgba(79, 172, 254, 0.05);
}

.drag-drop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
}

.upload-icon {
  width: 2rem;
  height: 2rem;
  color: var(--text-muted);
}

.drag-drop-text {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin: 0;
}

/* File Preview Styles */
.file-preview {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--bg-card);
  border-radius: 0.5rem;
  border: 1px solid var(--border-primary);
  position: relative;
}

.preview-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 0.375rem;
  border: 1px solid var(--border-secondary);
}

.preview-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.preview-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  word-break: break-all;
}

.preview-size {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.remove-preview-btn {
  position: absolute;
  top: var(--space-1);
  right: var(--space-1);
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-preview-btn:hover {
  background: #ef4444;
  transform: scale(1.1);
}

.remove-preview-btn .icon {
  width: 0.875rem;
  height: 0.875rem;
}

/* Validation Styles */
.file-validation {
  margin-top: var(--space-2);
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  padding: var(--space-1) var(--space-2);
  background: rgba(239, 68, 68, 0.1);
  border-radius: 0.25rem;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.form-input.error,
.form-textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.validation-error {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: var(--space-1);
}

/* Character Count Styles */
.character-count {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-left: auto;
  font-weight: 500;
  padding: var(--space-1) var(--space-2);
  background: var(--bg-card);
  border-radius: 0.25rem;
  border: 1px solid var(--border-secondary);
}

.character-count.warning {
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

/* Tooltip Styles */
.field-tooltip {
  display: inline-flex;
  align-items: center;
  margin-left: var(--space-1);
  cursor: help;
  color: var(--text-muted);
}

.field-tooltip:hover {
  color: var(--electric-blue);
}

.icon-small {
  width: 0.875rem;
  height: 0.875rem;
}

/* Loading Overlay Styles */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.loading-content {
  background: var(--bg-card);
  padding: var(--space-8);
  border-radius: 1rem;
  text-align: center;
  border: 1px solid var(--border-primary);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid var(--border-secondary);
  border-top: 3px solid var(--electric-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-4);
}

.loading-text {
  color: var(--text-primary);
  font-size: 1rem;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Button Spinner Styles */
.button-spinner {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Draft Button Styles */
.draft-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: var(--space-3);
}

.draft-button:hover {
  background: var(--bg-card-hover);
  border-color: var(--electric-blue);
  color: var(--text-primary);
}

.draft-button .icon {
  width: 1rem;
  height: 1rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.modal-dialog {
  background: var(--bg-card);
  border-radius: 0.75rem;
  border: 1px solid var(--border-primary);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  padding: var(--space-6) var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--border-secondary);
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-body {
  padding: var(--space-4) var(--space-6);
}

.modal-message {
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.modal-footer {
  padding: var(--space-4) var(--space-6) var(--space-6);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

.modal-btn {
  padding: var(--space-2) var(--space-4);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.modal-btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border-color: var(--border-primary);
}

.modal-btn-secondary:hover {
  background: var(--bg-card-hover);
  color: var(--text-primary);
}

.modal-btn-danger {
  background: #ef4444;
  color: white;
}

.modal-btn-danger:hover {
  background: #dc2626;
}

/* Notification Styles */
.notification {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  padding: var(--space-3) var(--space-4);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 10000;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  max-width: 400px;
  animation: slideIn 0.3s ease-out;
}

.notification-success {
  border-left: 4px solid #10b981;
}

.notification-error {
  border-left: 4px solid #ef4444;
}

.notification-message {
  color: var(--text-primary);
  font-size: 0.875rem;
  flex: 1;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.notification-close:hover {
  color: var(--text-primary);
}

.notification-close .icon {
  width: 1rem;
  height: 1rem;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Meme Page Styles */
.meme-header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-4) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.meme-header-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.meme-main {
  padding: var(--space-8) 0;
  min-height: calc(100vh - 80px);
}

.meme-info {
  margin-bottom: var(--space-8);
  text-align: center;
}

.meme-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
  font-size: 0.875rem;
}

.meme-breadcrumb a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.meme-breadcrumb a:hover {
  color: var(--electric-blue);
}

.breadcrumb-separator {
  color: var(--text-muted);
}

.meme-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.meme-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.meme-collection {
  padding: var(--space-1) var(--space-3);
  background: var(--bg-secondary);
  border-radius: 1rem;
  border: 1px solid var(--border-secondary);
}

.meme-display {
  margin-bottom: var(--space-8);
}

.meme-container {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  border-radius: 1rem;
  overflow: hidden;
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.meme-image {
  width: 100%;
  height: auto;
  display: block;
  max-height: 80vh;
  object-fit: contain;
}

.meme-actions {
  position: absolute;
  top: var(--space-3);
  right: var(--space-3);
  display: flex;
  gap: var(--space-2);
}

.action-btn {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.action-btn .icon {
  width: 1.25rem;
  height: 1.25rem;
}

.meme-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-6);
  max-width: 600px;
  margin: 0 auto;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.nav-btn:hover:not(:disabled) {
  background: var(--bg-card-hover);
  border-color: var(--electric-blue);
  transform: translateY(-1px);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-btn .icon {
  width: 1rem;
  height: 1rem;
}

.meme-counter {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  padding: var(--space-2) var(--space-4);
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  border: 1px solid var(--border-secondary);
}

/* Fullscreen Modal */
.fullscreen-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.fullscreen-content {
  position: relative;
  max-width: 95vw;
  max-height: 95vh;
}

.fullscreen-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 0.5rem;
}

.fullscreen-close {
  position: absolute;
  top: -3rem;
  right: 0;
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.fullscreen-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.fullscreen-close .icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .meme-header-content {
    gap: var(--space-2);
  }

  .meme-title {
    font-size: 1.5rem;
  }

  .meme-meta {
    flex-direction: column;
    gap: var(--space-2);
  }

  .meme-navigation {
    gap: var(--space-3);
  }

  .nav-btn {
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
  }

  .meme-actions {
    top: var(--space-2);
    right: var(--space-2);
  }

  .action-btn {
    width: 2rem;
    height: 2rem;
  }

  .action-btn .icon {
    width: 1rem;
    height: 1rem;
  }
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  padding: var(--space-3);
}

.login-button {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--gradient-accent);
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  min-height: 44px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.login-button:hover {
  opacity: 0.9;
}

.login-footer {
  text-align: center;
  margin-top: var(--space-8);
}

.back-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: var(--text-primary);
}

.admin-panel {
  min-height: 100vh;
}

.admin-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-secondary);
}

.admin-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
  padding: 0 var(--space-4);
}

@media (max-width: 768px) {
  .admin-header-content {
    height: auto;
    min-height: 3.5rem;
    padding: var(--space-3);
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .admin-logo-section {
    justify-content: center;
  }

  .admin-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .admin-actions .admin-btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }
}

.admin-logo-section {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.admin-logo {
  width: 2rem;
  height: 2rem;
  background: var(--gradient-accent);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

.admin-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.admin-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.admin-btn.secondary {
  background: transparent;
  border: 1px solid var(--border-primary);
}

.admin-container {
  padding: var(--space-8) var(--space-4);
}

@media (max-width: 768px) {
  .admin-container {
    padding: var(--space-6) var(--space-4);
  }
}

.upload-section {
  margin-bottom: var(--space-12);
}

.section-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  text-align: center;
}

@media (max-width: 768px) {
  .upload-section {
    margin-bottom: var(--space-8);
  }

  .section-title {
    font-size: 1.25rem;
    margin-bottom: var(--space-4);
    text-align: center;
  }
}

.upload-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  max-width: 100%;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .upload-form {
    gap: var(--space-4);
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
}

@media (min-width: 768px) {
  .form-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

.memes-section-form {
  border: 1px solid var(--border-secondary);
  border-radius: 0.5rem;
  padding: var(--space-6);
  background: var(--bg-card);
}

.memes-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.memes-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.add-meme-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--gradient-accent);
  border: none;
  border-radius: 0.375rem;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.add-meme-btn:hover {
  opacity: 0.9;
}

.memes-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.meme-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  padding: var(--space-6);
  background: var(--bg-secondary);
  border: 1px solid var(--border-secondary);
  border-radius: 0.75rem;
  position: relative;
  margin-bottom: var(--space-4);
}

.meme-input-group .form-group {
  margin-bottom: 0;
}

.meme-input-group .form-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.meme-input-group .drag-drop-zone {
  min-height: 140px;
}

.meme-input-group .form-divider {
  margin: var(--space-3) 0;
}

.meme-input-group .form-input {
  width: 100%;
}

.meme-input-group .file-upload-section {
  margin-bottom: var(--space-2);
}

.meme-input-group .validation-message {
  margin-top: var(--space-1);
  font-size: 0.75rem;
  color: #ef4444;
}

.remove-meme-btn {
  position: absolute;
  top: var(--space-3);
  right: var(--space-3);
  padding: var(--space-2);
  background: rgba(239, 68, 68, 0.9);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.remove-meme-btn:hover {
  background: #ef4444;
  transform: scale(1.1);
}

.remove-meme-btn .icon {
  width: 1rem;
  height: 1rem;
}

/* Mobile Responsive for Admin Panel */
@media (max-width: 768px) {
  .meme-input-group {
    padding: var(--space-4);
    gap: var(--space-3);
  }

  .meme-input-group .drag-drop-zone {
    min-height: 120px;
    padding: var(--space-4);
  }

  .meme-input-group .form-label {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .character-count {
    align-self: flex-end;
  }

  .remove-meme-btn {
    top: var(--space-2);
    right: var(--space-2);
    width: 1.75rem;
    height: 1.75rem;
  }

  .remove-meme-btn .icon {
    width: 0.875rem;
    height: 0.875rem;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-secondary);
  gap: var(--space-3);
  align-items: center;
}

.submit-button {
  padding: var(--space-3) var(--space-6);
  background: var(--gradient-accent);
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  min-height: 44px;
  cursor: pointer;
  transition: opacity 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.submit-button:hover {
  opacity: 0.9;
}

@media (max-width: 768px) {
  .form-actions {
    flex-direction: column-reverse;
    gap: var(--space-3);
    align-items: stretch;
    padding-top: var(--space-4);
  }

  .submit-button,
  .draft-button {
    width: 100%;
    justify-content: center;
    padding: var(--space-3) var(--space-4);
    margin-right: 0;
  }

  .draft-button {
    margin-bottom: 0;
  }
}

.existing-series {
  margin-top: var(--space-12);
}

@media (max-width: 768px) {
  .existing-series {
    margin-top: var(--space-8);
  }
}

.admin-series-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
}

@media (min-width: 768px) {
  .admin-series-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .admin-series-list {
    grid-template-columns: repeat(3, 1fr);
  }
}

.admin-series-card {
  padding: var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-secondary);
  border-radius: 0.75rem;
}

@media (max-width: 768px) {
  .admin-series-card {
    padding: var(--space-3);
  }
}

.admin-series-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.admin-series-meta {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--space-3);
}

.admin-series-actions {
  display: flex;
  gap: var(--space-2);
}

@media (max-width: 768px) {
  .admin-series-actions {
    flex-direction: column;
    gap: var(--space-2);
  }
}

.admin-series-btn {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: 0.375rem;
  background: transparent;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 768px) {
  .admin-series-btn {
    padding: var(--space-2) var(--space-4);
    font-size: 0.875rem;
    min-height: 44px;
    width: 100%;
  }
}

.admin-series-btn:hover {
  color: var(--text-primary);
  background: var(--bg-card-hover);
}

.admin-series-btn.delete {
  border-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.admin-series-btn.delete:hover {
  background: rgba(239, 68, 68, 0.1);
}

/* Comprehensive Mobile Responsive Design */
@media (max-width: 768px) {
  /* Ensure all content has proper spacing from screen edges */
  .container {
    padding-left: var(--space-4);
    padding-right: var(--space-4);
  }

  /* Fix any remaining form elements */
  .file-upload-btn {
    padding: var(--space-2) var(--space-3);
    font-size: 0.875rem;
    width: 100%;
    justify-content: center;
  }

  /* Improve tooltip positioning on mobile */
  .field-tooltip {
    position: relative;
  }

  /* Better spacing for validation messages */
  .validation-error,
  .file-validation {
    margin-top: var(--space-1);
    font-size: 0.75rem;
  }

  /* Ensure proper touch targets */
  button,
  .btn,
  .admin-btn,
  .back-link {
    min-height: 44px;
    min-width: 44px;
  }

  /* Fix any text overflow issues */
  .admin-series-title,
  .section-title {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

@media (max-width: 639px) {
  .hero-title {
    font-size: 2rem;
  }

  .stats-grid {
    gap: var(--space-4);
  }

  .series-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    flex-direction: column;
  }

  .modal-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--border-secondary);
  }
}

/* Utility Classes */
.hidden { display: none !important; }
.visible { display: block !important; }

/* Line Clamp Utility */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
