<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collection - MemesByKayé</title>
    <meta name="description" content="A collection of memes from MemesByKayé's curated gallery.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:title" content="Collection - MemesByKayé">
    <meta property="og:description" content="A collection of memes from MemesByKayé's curated gallery.">
    <meta property="og:image" content="">
    <meta property="og:site_name" content="MemesByKayé">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="">
    <meta name="twitter:title" content="Collection - MemesByKayé">
    <meta name="twitter:description" content="A collection of memes from MemesByKayé's curated gallery.">
    <meta name="twitter:image" content="">

    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3ClinearGradient id='grad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%234facfe;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%2300f2fe;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='32' height='32' rx='8' fill='url(%23grad)'/%3E%3Ccircle cx='16' cy='16' r='12' fill='none' stroke='rgba(255,255,255,0.2)' stroke-width='1'/%3E%3Ctext x='16' y='22' text-anchor='middle' fill='white' font-family='Inter, sans-serif' font-weight='bold' font-size='16'%3EK%3C/text%3E%3C/svg%3E">
    <link rel="stylesheet" href="/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="series-header">
        <div class="container">
            <div class="series-header-content">
                <a href="/home" class="back-btn">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    <span>Back to Collection</span>
                </a>
                
                <div class="view-controls">
                    <button id="view-toggle" class="view-toggle-btn">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                        </svg>
                        <span>List</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="container series-container">
        <!-- Series Info -->
        <div class="series-info" id="series-info">
            <!-- Series details will be loaded here -->
        </div>

        <!-- Memes Grid -->
        <div class="memes-section">
            <h2 class="memes-title">Memes in this Collection</h2>
            <div class="memes-grid" id="memes-grid">
                <!-- Memes will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Meme Modal -->
    <div class="modal-overlay" id="meme-modal" style="display: none;">
        <div class="modal-container">
            <div class="modal-header">
                <div class="modal-title-section">
                    <h2 class="modal-title" id="modal-title">Meme Title</h2>
                    <p class="modal-description" id="modal-description"></p>
                </div>
                
                <div class="modal-actions">
                    <button id="share-btn" class="modal-action-btn" title="Share meme">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                    </button>
                    
                    <button id="close-modal" class="modal-action-btn" title="Close modal">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <div class="modal-content">
                <div class="modal-image-section">
                    <div class="modal-image-container">
                        <img id="modal-image" src="" alt="" class="modal-image">
                        <div class="modal-loading" id="modal-loading">
                            <div class="loading-spinner"></div>
                        </div>
                    </div>
                </div>

                <div class="modal-sidebar">
                    <div class="modal-meta">
                        <div class="meta-item">
                            <span class="meta-label">Created</span>
                            <span class="meta-value" id="modal-date">-</span>
                        </div>
                        <div class="meta-item" id="modal-tags-container" style="display: none;">
                            <span class="meta-label">Tags</span>
                            <div class="meta-tags" id="modal-tags"></div>
                        </div>
                    </div>

                    <div class="modal-actions-bottom">
                        <button id="download-btn" class="action-button">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m5-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Download
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Client -->
    <script src="/lib/api-client.js"></script>
    <!-- Main Application Script -->
    <script src="/script-serverless.js"></script>
</body>
</html>
