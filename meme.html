<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meme - MemesByKayé</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3ClinearGradient id='grad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%234facfe;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%2300f2fe;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='32' height='32' rx='8' fill='url(%23grad)'/%3E%3Ccircle cx='16' cy='16' r='12' fill='none' stroke='rgba(255,255,255,0.2)' stroke-width='1'/%3E%3Ctext x='16' y='22' text-anchor='middle' fill='white' font-family='Inter, sans-serif' font-weight='bold' font-size='16'%3EK%3C/text%3E%3C/svg%3E">
    <link rel="stylesheet" href="/styles.css">
</head>
<body>
    <!-- Header -->
    <header class="meme-header">
        <div class="container">
            <div class="meme-header-content">
                <a href="/home" class="back-btn" id="back-to-home">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span>Home</span>
                </a>

                <a href="#" class="back-btn" id="back-to-collection">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    <span>Back to Collection</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="meme-main">
        <div class="container">
            <!-- Meme Info -->
            <div class="meme-info">
                <div class="meme-breadcrumb">
                    <a href="/home">Home</a>
                    <span class="breadcrumb-separator">›</span>
                    <a href="#" id="collection-breadcrumb">Collection</a>
                    <span class="breadcrumb-separator">›</span>
                    <span id="meme-breadcrumb">Meme</span>
                </div>
                
                <h1 class="meme-title" id="meme-title">Loading...</h1>
                <div class="meme-meta">
                    <span class="meme-collection" id="meme-collection">Collection</span>
                    <span class="meme-date" id="meme-date">Date</span>
                </div>
            </div>

            <!-- Meme Display -->
            <div class="meme-display">
                <div class="meme-container">
                    <img id="meme-image" src="" alt="" class="meme-image">
                    <div class="meme-actions">
                        <button class="action-btn" id="share-btn" title="Share meme">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                            </svg>
                        </button>
                        <button class="action-btn" id="fullscreen-btn" title="View fullscreen">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="meme-navigation">
                <button class="nav-btn" id="prev-btn" disabled>
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                    Previous
                </button>
                
                <span class="meme-counter" id="meme-counter">1 of 1</span>
                
                <button class="nav-btn" id="next-btn" disabled>
                    Next
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
        </div>
    </main>

    <!-- Fullscreen Modal -->
    <div class="fullscreen-modal" id="fullscreen-modal" style="display: none;">
        <div class="fullscreen-content">
            <button class="fullscreen-close" id="fullscreen-close">
                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
            <img id="fullscreen-image" src="" alt="" class="fullscreen-image">
        </div>
    </div>

    <!-- API Client -->
    <script src="/lib/api-client.js"></script>
    <!-- Main Application Script -->
    <script src="/script-serverless.js"></script>
</body>
</html>
