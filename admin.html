<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - MemesByKayé</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cdefs%3E%3ClinearGradient id='grad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%234facfe;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%2300f2fe;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='32' height='32' rx='8' fill='url(%23grad)'/%3E%3Ccircle cx='16' cy='16' r='12' fill='none' stroke='rgba(255,255,255,0.2)' stroke-width='1'/%3E%3Ctext x='16' y='22' text-anchor='middle' fill='white' font-family='Inter, sans-serif' font-weight='bold' font-size='16'%3EK%3C/text%3E%3C/svg%3E">
    <link rel="stylesheet" href="/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Login Form -->
    <div class="admin-login" id="admin-login">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <div class="login-logo">
                        <span>K</span>
                    </div>
                    <h1 class="login-title">Admin Access</h1>
                    <p class="login-description">Enter password to manage your meme collection</p>
                </div>

                <form id="login-form" class="login-form">
                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <input
                            id="password"
                            type="password"
                            placeholder="Enter admin password"
                            class="form-input"
                            required
                        />
                    </div>
                    
                    <div id="login-error" class="error-message" style="display: none;"></div>

                    <button type="submit" class="login-button">
                        Access Admin Panel
                    </button>
                </form>

                <div class="login-footer">
                    <a href="/home" class="back-link">
                        ← Back to Collection
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Panel -->
    <div class="admin-panel" id="admin-panel" style="display: none;">
        <!-- Header -->
        <header class="admin-header">
            <div class="container">
                <div class="admin-header-content">
                    <div class="admin-logo-section">
                        <div class="admin-logo">K</div>
                        <h1 class="admin-title">Admin Panel</h1>
                    </div>
                    
                    <div class="admin-actions">
                        <a href="/home" class="admin-btn secondary">
                            View Site
                        </a>
                        <button id="logout-btn" class="admin-btn">
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="container admin-container">
            <!-- Upload Section -->
            <div class="upload-section">
                <h2 class="section-title">Upload New Collection</h2>
                
                <form id="upload-form" class="upload-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="series-title" class="form-label">Collection Title</label>
                            <input
                                id="series-title"
                                type="text"
                                placeholder="Enter collection title"
                                class="form-input"
                                required
                            />
                        </div>

                        <div class="form-group">
                            <label for="series-characters" class="form-label">Total Characters</label>
                            <input
                                id="series-characters"
                                type="text"
                                placeholder="Character names (comma separated)"
                                class="form-input"
                                required
                            />
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="series-description" class="form-label">Description</label>
                        <textarea
                            id="series-description"
                            placeholder="Enter collection description"
                            class="form-textarea"
                            rows="3"
                        ></textarea>
                    </div>

                    <div class="form-group">
                        <label for="cover-image" class="form-label">
                            Cover Image
                            <span class="field-tooltip" title="Upload a cover image for your collection. Recommended size: 300x400px. Max size: 5MB.">
                                <svg class="icon-small" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </span>
                        </label>
                        <div class="file-upload-section">
                            <div class="drag-drop-zone" id="cover-drag-zone">
                                <input
                                    id="cover-image-file"
                                    type="file"
                                    accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                                    class="file-input"
                                    style="display: none;"
                                />
                                <div class="drag-drop-content">
                                    <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                    </svg>
                                    <p class="drag-drop-text">Drag & drop your cover image here</p>
                                    <button type="button" class="file-upload-btn" onclick="document.getElementById('cover-image-file').click()">
                                        Choose File
                                    </button>
                                </div>
                                <div class="file-preview" id="cover-preview" style="display: none;">
                                    <img class="preview-image" id="cover-preview-img" src="" alt="Cover preview">
                                    <div class="preview-info">
                                        <span class="preview-name" id="cover-preview-name"></span>
                                        <span class="preview-size" id="cover-preview-size"></span>
                                    </div>
                                    <button type="button" class="remove-preview-btn" id="cover-remove-btn" title="Remove image">
                                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="file-validation" id="cover-validation"></div>
                        </div>
                        <div class="form-divider">OR</div>
                        <input
                            id="cover-image"
                            type="url"
                            placeholder="https://example.com/cover.jpg"
                            class="form-input"
                        />
                    </div>

                    <div class="memes-section-form">
                        <div class="memes-header">
                            <h3 class="memes-title">Memes</h3>
                            <button type="button" id="add-meme" class="add-meme-btn">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Add Meme
                            </button>
                        </div>
                        
                        <div id="memes-container" class="memes-container">
                            <!-- Meme inputs will be added here -->
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="draft-button" id="save-draft-btn">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            Save as Draft
                        </button>
                        <button type="submit" class="submit-button" id="create-collection-btn">
                            <span class="button-text">Create Collection</span>
                            <div class="button-spinner" style="display: none;">
                                <div class="spinner"></div>
                            </div>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Existing Collections -->
            <div class="existing-series">
                <h2 class="section-title">Existing Collections</h2>
                <div id="admin-series-list" class="admin-series-list">
                    <!-- Existing collections will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="loading-text">Processing your collection...</p>
        </div>
    </div>

    <!-- Confirmation Dialog -->
    <div class="modal-overlay" id="confirmation-modal" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title" id="confirmation-title">Confirm Action</h3>
            </div>
            <div class="modal-body">
                <p class="modal-message" id="confirmation-message">Are you sure you want to proceed?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="modal-btn modal-btn-secondary" id="confirmation-cancel">Cancel</button>
                <button type="button" class="modal-btn modal-btn-danger" id="confirmation-confirm">Confirm</button>
            </div>
        </div>
    </div>

    <!-- API Client -->
    <script src="/lib/api-client.js"></script>
    <!-- Main Application Script -->
    <script src="/script-serverless.js"></script>
</body>
</html>
