// Discord CDN Upload API for MemesByKayé
// Uploads images to Discord and returns CDN URLs

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).json({});
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { imageData, fileName, adminPassword } = req.body;

    // Verify admin password with trimming
    if (!adminPassword || adminPassword.trim() !== process.env.ADMIN_PASSWORD?.trim()) {
      console.log('Upload auth failed - provided:', adminPassword ? '[REDACTED]' : 'undefined', 'expected:', process.env.ADMIN_PASSWORD ? '[CONFIGURED]' : 'undefined');
      return res.status(401).json({ error: 'Unauthorized access' });
    }

    // Validate image data
    if (!imageData || !fileName) {
      return res.status(400).json({ error: 'Missing image data or filename' });
    }

    // Convert base64 to buffer
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');

    // Validate file size (5MB max)
    if (buffer.length > 5 * 1024 * 1024) {
      return res.status(400).json({ error: 'File too large. Maximum size is 5MB.' });
    }

    // Upload to Discord webhook
    const discordUrl = await uploadToDiscord(buffer, fileName);

    return res.status(200).json({
      success: true,
      url: discordUrl,
      fileName: fileName,
      size: buffer.length
    });

  } catch (error) {
    console.error('Upload error:', error);
    return res.status(500).json({ error: 'Upload failed: ' + error.message });
  }
}

async function uploadToDiscord(buffer, fileName) {
  const webhookUrl = process.env.DISCORD_WEBHOOK_URL;

  if (!webhookUrl) {
    throw new Error('Discord webhook URL not configured');
  }

  try {
    // Import form-data for Node.js environment
    const FormData = require('form-data');
    const form = new FormData();

    // Add the file
    form.append('file', buffer, {
      filename: fileName,
      contentType: getContentType(fileName)
    });

    // Add a message (optional)
    form.append('content', `📸 New meme uploaded: ${fileName}`);

    // Use node-fetch for Node.js environment
    const fetch = require('node-fetch');

    // Upload to Discord
    const response = await fetch(webhookUrl, {
      method: 'POST',
      body: form,
      headers: form.getHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Discord upload failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    // Extract CDN URL from Discord response
    if (result.attachments && result.attachments.length > 0) {
      return result.attachments[0].url;
    }

    throw new Error('No attachment URL returned from Discord');

  } catch (error) {
    console.error('Discord upload error:', error);
    throw error;
  }
}

function getContentType(fileName) {
  const ext = fileName.toLowerCase().split('.').pop();
  const types = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp'
  };
  return types[ext] || 'image/jpeg';
}
