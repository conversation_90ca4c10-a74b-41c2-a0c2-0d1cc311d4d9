// Authentication API for MemesByKayé
// Validates admin password against environment variable

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export default async function handler(req, res) {
  // Set CORS headers for all responses
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return res.status(200).json({});
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({ error: 'Password is required' });
    }

    // Trim passwords to avoid whitespace issues
    const trimmedPassword = password.trim();
    const adminPassword = (process.env.ADMIN_PASSWORD || 'Kuku1010@').trim();

    if (trimmedPassword === adminPassword) {
      return res.status(200).json({
        success: true,
        message: 'Authentication successful'
      });
    } else {
      console.log('Auth failed - provided:', trimmedPassword ? '[REDACTED]' : 'undefined', 'expected:', adminPassword ? '[CONFIGURED]' : 'undefined');
      return res.status(401).json({
        success: false,
        error: 'Invalid password'
      });
    }

  } catch (error) {
    console.error('Auth error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
