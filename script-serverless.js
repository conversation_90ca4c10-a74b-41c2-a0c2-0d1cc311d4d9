// MemesByKayé - Serverless Edition
// Uses GitHub + Vercel + Discord CDN instead of Firebase

// Global state and configuration
const CONFIG = {
    ADMIN_KEY: 'memesByKaye_admin'
};

// Metadata Manager for Social Media Sharing
class MetadataManager {
    static updateMetadata(metadata) {
        console.log('DEBUG: updateMetadata called with:', metadata);

        const {
            title = 'MemesByKayé - Personal Meme Collection',
            description = 'A personal collection of memes featuring <PERSON><PERSON> and friends. Discover hilarious moments and reactions in this curated meme gallery.',
            image = null,
            url = window.location.href,
            type = 'website'
        } = metadata;

        console.log('DEBUG: Final metadata values:', { title, description, image, url, type });

        // Update page title
        document.title = title;

        // Update or create meta tags
        this.setMetaTag('description', description);
        this.setMetaTag('og:title', title);
        this.setMetaTag('og:description', description);
        this.setMetaTag('og:type', type);
        this.setMetaTag('og:url', url);
        this.setMetaTag('og:site_name', 'MemesByKayé');
        this.setMetaTag('twitter:card', 'summary_large_image');
        this.setMetaTag('twitter:title', title);
        this.setMetaTag('twitter:description', description);

        // Set images with absolute URLs
        if (image) {
            const absoluteImageUrl = this.ensureAbsoluteUrl(image);
            this.setMetaTag('og:image', absoluteImageUrl);
            this.setMetaTag('twitter:image', absoluteImageUrl);
            this.setMetaTag('og:image:alt', title);
            this.setMetaTag('twitter:image:alt', title);
        }
    }

    static setMetaTag(property, content) {
        // Handle different meta tag types
        let selector, attributeName;

        if (property.startsWith('og:') || property.startsWith('twitter:')) {
            selector = `meta[property="${property}"], meta[name="${property}"]`;
            attributeName = property.startsWith('og:') ? 'property' : 'name';
        } else {
            selector = `meta[name="${property}"]`;
            attributeName = 'name';
        }

        let metaTag = document.querySelector(selector);

        if (!metaTag) {
            metaTag = document.createElement('meta');
            metaTag.setAttribute(attributeName, property);
            document.head.appendChild(metaTag);
        }

        metaTag.setAttribute('content', content);
    }

    static ensureAbsoluteUrl(url) {
        if (!url) return null;

        // If already absolute, return as-is
        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }

        // If relative, make absolute
        if (url.startsWith('/')) {
            return `${window.location.origin}${url}`;
        }

        // If no protocol and no leading slash, assume it's a relative path
        return `${window.location.origin}/${url}`;
    }

    static setDefaultMetadata() {
        this.updateMetadata({
            title: 'MemesByKayé - Personal Meme Collection',
            description: 'A personal collection of memes featuring Kayé and friends. Discover hilarious moments and reactions in this curated meme gallery.',
            type: 'website'
        });
    }

    static setCollectionMetadata(series) {
        console.log('DEBUG: Setting collection metadata for:', series);

        const memeCount = series.memes?.length || 0;
        const description = series.description ||
            `A collection of ${memeCount} memes featuring ${series.characters?.join(', ') || 'various characters'}. ${series.title} - part of MemesByKayé's curated meme gallery.`;

        console.log('DEBUG: Collection metadata:', {
            title: `${series.title} - MemesByKayé`,
            description: description,
            image: series.coverImage
        });

        this.updateMetadata({
            title: `${series.title} - MemesByKayé`,
            description: description,
            image: series.coverImage,
            type: 'website'
        });
    }

    static setMemeMetadata(meme, series) {
        console.log('DEBUG: Setting meme metadata for:', meme, 'in series:', series);

        const description = meme.description ||
            `${meme.title} from the ${series.title} collection. Part of MemesByKayé's curated meme gallery featuring ${series.characters?.join(', ') || 'various characters'}.`;

        console.log('DEBUG: Meme metadata:', {
            title: `${meme.title} - ${series.title} - MemesByKayé`,
            description: description,
            image: meme.imageUrl
        });

        this.updateMetadata({
            title: `${meme.title} - ${series.title} - MemesByKayé`,
            description: description,
            image: meme.imageUrl,
            type: 'article'
        });
    }
}

// Data Manager using Serverless API
class ServerlessDataManager {
    static async getData() {
        try {
            return await window.apiClient.getCollections();
        } catch (error) {
            console.error('Error fetching data:', error);
            return { series: [] };
        }
    }

    static async saveCollection(collectionData) {
        try {
            return await window.apiClient.createCollection(collectionData);
        } catch (error) {
            console.error('Error saving collection:', error);
            throw error;
        }
    }

    static async updateCollection(collectionId, updates) {
        try {
            return await window.apiClient.updateCollection(collectionId, updates);
        } catch (error) {
            console.error('Error updating collection:', error);
            throw error;
        }
    }

    static async deleteCollection(collectionId) {
        try {
            return await window.apiClient.deleteCollection(collectionId);
        } catch (error) {
            console.error('Error deleting collection:', error);
            throw error;
        }
    }

    static isAdminAuthenticated() {
        return localStorage.getItem(CONFIG.ADMIN_KEY) === 'true';
    }

    static setAdminAuthenticated(value, password = null) {
        if (value) {
            localStorage.setItem(CONFIG.ADMIN_KEY, 'true');
            // Store password for API calls (simple encoding, not for security)
            if (password) {
                localStorage.setItem(CONFIG.ADMIN_KEY + '_pwd', btoa(password));
            }
        } else {
            localStorage.removeItem(CONFIG.ADMIN_KEY);
            localStorage.removeItem(CONFIG.ADMIN_KEY + '_pwd');
        }
    }

    static getStoredPassword() {
        const stored = localStorage.getItem(CONFIG.ADMIN_KEY + '_pwd');
        return stored ? atob(stored) : null;
    }
}

// File Manager using Discord CDN
class ServerlessFileManager {
    static async uploadImage(file) {
        try {
            return await window.apiClient.uploadImageWithCompression(file);
        } catch (error) {
            throw error;
        }
    }

    static validateFile(file) {
        return window.apiClient.validateFile(file);
    }

    static formatFileSize(bytes) {
        return window.apiClient.formatFileSize(bytes);
    }

    static async saveCoverImage(file) {
        return this.uploadImage(file);
    }

    static async saveMemeImage(file) {
        return this.uploadImage(file);
    }

    static async uploadImageWithMetadata(file, metadata) {
        try {
            return await window.apiClient.uploadImageWithCompression(file, metadata);
        } catch (error) {
            throw error;
        }
    }
}

// Validation utility (unchanged)
class Validator {
    static validateTitle(title) {
        const errors = [];
        if (!title || title.trim().length === 0) {
            errors.push('Title is required');
        } else if (title.trim().length < 3) {
            errors.push('Title must be at least 3 characters');
        } else if (title.trim().length > 100) {
            errors.push('Title must be less than 100 characters');
        }
        return { isValid: errors.length === 0, errors };
    }

    static validateCharacters(characters) {
        const errors = [];
        if (!characters || characters.trim().length === 0) {
            errors.push('At least one character is required');
        }
        return { isValid: errors.length === 0, errors };
    }

    static validateDescription(description) {
        const errors = [];
        if (description && description.length > 500) {
            errors.push('Description must be less than 500 characters');
        }
        return { isValid: errors.length === 0, errors };
    }

    static validateUrl(url) {
        const errors = [];
        if (url && url.trim().length > 0) {
            try {
                new URL(url);
            } catch {
                errors.push('Please enter a valid URL');
            }
        }
        return { isValid: errors.length === 0, errors };
    }
}

// UI utility (unchanged)
class UIManager {
    static showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        const text = overlay?.querySelector('.loading-text');
        if (text) text.textContent = message;
        if (overlay) overlay.style.display = 'flex';
    }

    static hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) overlay.style.display = 'none';
    }

    static showConfirmation(title, message, onConfirm, onCancel = null) {
        const modal = document.getElementById('confirmation-modal');
        const titleEl = document.getElementById('confirmation-title');
        const messageEl = document.getElementById('confirmation-message');
        const confirmBtn = document.getElementById('confirmation-confirm');
        const cancelBtn = document.getElementById('confirmation-cancel');

        if (titleEl) titleEl.textContent = title;
        if (messageEl) messageEl.textContent = message;
        if (modal) modal.style.display = 'flex';

        const handleConfirm = () => {
            if (modal) modal.style.display = 'none';
            confirmBtn?.removeEventListener('click', handleConfirm);
            cancelBtn?.removeEventListener('click', handleCancel);
            onConfirm();
        };

        const handleCancel = () => {
            if (modal) modal.style.display = 'none';
            confirmBtn?.removeEventListener('click', handleConfirm);
            cancelBtn?.removeEventListener('click', handleCancel);
            if (onCancel) onCancel();
        };

        confirmBtn?.addEventListener('click', handleConfirm);
        cancelBtn?.addEventListener('click', handleCancel);
    }

    static showValidationError(element, message) {
        element.classList.add('error');
        let errorEl = element.parentNode.querySelector('.validation-error');
        if (!errorEl) {
            errorEl = document.createElement('div');
            errorEl.className = 'validation-error';
            element.parentNode.appendChild(errorEl);
        }
        errorEl.textContent = message;
    }

    static clearValidationError(element) {
        element.classList.remove('error');
        const errorEl = element.parentNode.querySelector('.validation-error');
        if (errorEl) {
            errorEl.remove();
        }
    }

    static showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Utility functions (unchanged)
class Utils {
    static formatDate(dateString) {
        return new Date(dateString).toLocaleDateString();
    }

    static generateId() {
        return 'id-' + Math.random().toString(36).substr(2, 9);
    }

    static slugify(text) {
        return text
            .toLowerCase()
            .trim()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
            .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
    }

    static findSeriesBySlug(slug, data) {
        return data.series.find(s => Utils.slugify(s.title) === slug);
    }

    static findMemeBySlug(slug, series) {
        return (series.memes || []).find(m => Utils.slugify(m.title) === slug);
    }

    static createPlaceholderSVG(width = 300, height = 400, text = 'Placeholder') {
        return `data:image/svg+xml,${encodeURIComponent(`
            <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="placeholderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#1a1a24" />
                        <stop offset="100%" stop-color="#111118" />
                    </linearGradient>
                    <pattern id="dots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                        <circle cx="10" cy="10" r="1" fill="#ffffff" opacity="0.1" />
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#placeholderGradient)" />
                <rect width="100%" height="100%" fill="url(#dots)" />
                <rect x="1" y="1" width="${width - 2}" height="${height - 2}" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.1" />
                <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="#ffffff" font-size="14" font-family="Inter, system-ui, sans-serif" font-weight="400" opacity="0.3">${text}</text>
            </svg>
        `)}`;
    }

    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Main page functionality with Serverless API
class HomePage {
    constructor() {
        this.data = { series: [] };
        this.refreshInterval = null;
        this.init();
    }

    async init() {
        UIManager.showLoading('Loading collections...');

        try {
            await this.loadData();
            this.updateStats();
            this.renderSeriesGrid();
            this.setupEventListeners();

            // Set default metadata for homepage
            MetadataManager.setDefaultMetadata();

            // Set up periodic refresh (every 30 seconds)
            this.refreshInterval = setInterval(() => {
                this.loadData(true); // Silent refresh
            }, 30000);

        } catch (error) {
            UIManager.showNotification('Error loading collections', 'error');
        } finally {
            UIManager.hideLoading();
        }
    }

    async loadData(silent = false) {
        try {
            if (!silent) UIManager.showLoading('Loading collections...');

            this.data = await ServerlessDataManager.getData();

            if (!silent) {
                this.updateStats();
                this.renderSeriesGrid();
            }
        } catch (error) {
            console.error('Error loading data:', error);
            if (!silent) {
                UIManager.showNotification('Error loading collections', 'error');
            }
        } finally {
            if (!silent) UIManager.hideLoading();
        }
    }

    updateStats() {
        const seriesCount = this.data.series.length;
        const memesCount = this.data.series.reduce((total, series) => total + (series.memes?.length || 0), 0);

        const seriesCountEl = document.getElementById('series-count');
        const memesCountEl = document.getElementById('memes-count');

        if (seriesCountEl) seriesCountEl.textContent = seriesCount;
        if (memesCountEl) memesCountEl.textContent = memesCount;
    }

    renderSeriesGrid() {
        const grid = document.getElementById('series-grid');
        const emptyState = document.getElementById('empty-state');

        if (!grid) return;

        if (this.data.series.length === 0) {
            grid.style.display = 'none';
            if (emptyState) emptyState.style.display = 'block';
            return;
        }

        grid.style.display = 'grid';
        if (emptyState) emptyState.style.display = 'none';

        grid.innerHTML = this.data.series.map(series => this.createSeriesCard(series)).join('');
    }

    createSeriesCard(series) {
        // Use placeholder if no cover image or if cover image is empty
        const imageUrl = (series.coverImage && series.coverImage.trim() !== '')
            ? series.coverImage
            : Utils.createPlaceholderSVG(300, 400, series.title);
        const memeCount = series.memes?.length || 0;
        const description = series.description || `A collection of ${memeCount} memes featuring ${series.characters?.join(', ') || 'various characters'}.`;

        return `
            <a href="/${Utils.slugify(series.title)}" class="series-card card">
                <div class="series-cover">
                    <img src="${imageUrl}" alt="${series.title}" class="series-image" loading="lazy"
                         onerror="this.src='${Utils.createPlaceholderSVG(300, 400, series.title)}'">
                    <div class="series-overlay"></div>

                    <div class="series-badges">
                        <span class="series-badge k-badge">K</span>
                        <span class="series-badge count-badge">${memeCount} memes</span>
                    </div>

                    <div class="series-hover-overlay">
                        <div class="series-view-btn">View Series</div>
                    </div>
                </div>

                <div class="series-content">
                    <h3 class="series-title">${series.title}</h3>

                    <p class="series-description">${description}</p>

                    <div class="series-meta">
                        <div class="series-date">
                            <div class="series-date-dot"></div>
                            ${Utils.formatDate(series.releaseDate || series.createdAt)}
                        </div>
                        <div class="series-characters">
                            ${(series.characters || []).slice(0, 2).map(character =>
                                `<span class="character-tag">${character}</span>`
                            ).join('')}
                            ${(series.characters || []).length > 2 ?
                                `<span class="character-tag">+${series.characters.length - 2}</span>` : ''
                            }
                        </div>
                    </div>
                </div>
            </a>
        `;
    }

    setupEventListeners() {
        // Refresh button (if exists)
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadData());
        }

        // Clean up interval when page unloads
        window.addEventListener('beforeunload', () => {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
        });

        // Visibility change - refresh when tab becomes visible
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.loadData(true); // Silent refresh when tab becomes visible
            }
        });
    }

    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

// Initialize the appropriate page
document.addEventListener('DOMContentLoaded', () => {
    // Initialize API client if not already done
    if (!window.apiClient && window.APIClient) {
        window.apiClient = new APIClient();
    }

    // Wait for API client to be ready
    if (window.apiClient || window.APIClient) {
        initializeApp();
    } else {
        // Wait for API client to load
        const checkApiClient = setInterval(() => {
            if (window.APIClient) {
                window.apiClient = new APIClient();
                clearInterval(checkApiClient);
                initializeApp();
            } else if (window.apiClient) {
                clearInterval(checkApiClient);
                initializeApp();
            }
        }, 100);

        // Timeout after 5 seconds
        setTimeout(() => {
            clearInterval(checkApiClient);
            console.error('API client failed to load');
            if (window.UIManager) {
                UIManager.showNotification('Failed to initialize application', 'error');
            }
        }, 5000);
    }
});

function initializeApp() {
    try {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        const urlParams = new URLSearchParams(window.location.search);

        // Handle different page types
        // Check if this is a meme page (two path segments)
        const pathParts = path.substring(1).split('/').filter(part => part.length > 0);
        const isMemePageByPath = pathParts.length === 2;

        if (filename === 'meme.html' || (urlParams.has('collection') && urlParams.has('meme')) || isMemePageByPath) {
            // Individual meme page
            window.memePage = new MemePage();
        } else if (filename === 'series.html' || urlParams.has('collection') || (pathParts.length === 1 && path !== '/' && path !== '/home' && path !== '/admin')) {
            // Collection page
            window.seriesPage = new SeriesPage();
        } else if (path === '/admin' || filename === 'admin.html') {
            // Admin page
            window.adminPage = new AdminPage();
        } else {
            // Homepage
            window.homePage = new HomePage();
        }
    } catch (error) {
        console.error('Error initializing app:', error);
        if (window.UIManager) {
            UIManager.showNotification('Error initializing application', 'error');
        }
    }
}

// Simple Admin Page for serverless backend
class AdminPage {
    constructor() {
        this.data = { series: [] };
        this.init();
    }

    async init() {
        this.checkAuthentication();
        this.setupEventListeners();

        if (ServerlessDataManager.isAdminAuthenticated()) {
            await this.showAdminPanel();
        }
    }

    checkAuthentication() {
        const loginSection = document.getElementById('admin-login');
        const adminPanel = document.getElementById('admin-panel');

        if (ServerlessDataManager.isAdminAuthenticated()) {
            if (loginSection) loginSection.style.display = 'none';
            if (adminPanel) adminPanel.style.display = 'block';
        } else {
            if (loginSection) loginSection.style.display = 'flex';
            if (adminPanel) adminPanel.style.display = 'none';
        }
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }

        // Upload form
        const uploadForm = document.getElementById('upload-form');
        if (uploadForm) {
            uploadForm.addEventListener('submit', (e) => this.handleUpload(e));
        }

        // Add meme button
        const addMemeBtn = document.getElementById('add-meme');
        if (addMemeBtn) {
            addMemeBtn.addEventListener('click', () => this.addMemeInput());
        }

        // Setup file inputs
        this.setupCoverFileInput();
    }

    async handleLogin(e) {
        e.preventDefault();
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('login-error');
        const submitBtn = e.target.querySelector('button[type="submit"]');

        // Show loading state
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Verifying...';
        }

        try {
            // Authenticate with the server
            const result = await window.apiClient.authenticateAdmin(password);

            if (result.success) {
                ServerlessDataManager.setAdminAuthenticated(true, password);
                this.showAdminPanel();
                if (errorDiv) errorDiv.style.display = 'none';
            } else {
                if (errorDiv) {
                    errorDiv.textContent = result.error || 'Invalid password';
                    errorDiv.style.display = 'block';
                }
            }
        } catch (error) {
            console.error('Login error:', error);
            if (errorDiv) {
                errorDiv.textContent = 'Network error. Please try again.';
                errorDiv.style.display = 'block';
            }
        } finally {
            // Reset button state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Access Admin Panel';
            }
        }
    }

    handleLogout() {
        ServerlessDataManager.setAdminAuthenticated(false);
        window.location.reload();
    }

    async showAdminPanel() {
        const loginSection = document.getElementById('admin-login');
        const adminPanel = document.getElementById('admin-panel');

        if (loginSection) loginSection.style.display = 'none';
        if (adminPanel) adminPanel.style.display = 'block';

        // Restore password to API client if not present
        if (!window.apiClient.validatedPassword) {
            const storedPassword = ServerlessDataManager.getStoredPassword();
            if (storedPassword) {
                window.apiClient.validatedPassword = storedPassword;
            } else {
                UIManager.showNotification('Please log in again to continue', 'warning');
                this.handleLogout();
                return;
            }
        }

        this.addMemeInput(); // Add initial meme input
        await this.renderExistingSeries();
    }

    addMemeInput() {
        const container = document.getElementById('memes-container');
        if (!container) return;

        const memeGroup = document.createElement('div');
        const memeId = Utils.generateId();
        memeGroup.className = 'meme-input-group';
        memeGroup.innerHTML = `
            <div class="form-group">
                <label class="form-label">Meme Title</label>
                <input type="text" class="form-input meme-title" placeholder="Enter meme title" required>
            </div>
            <div class="form-group">
                <label class="form-label">Meme Image</label>
                <div class="file-upload-section">
                    <div class="drag-drop-zone meme-drag-zone">
                        <input type="file" class="file-input meme-file" accept="image/jpeg,image/jpg,image/png,image/gif,image/webp" style="display: none;">
                        <div class="drag-drop-content">
                            <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <p class="drag-drop-text">Drag & drop meme image here</p>
                            <button type="button" class="file-upload-btn" onclick="this.parentElement.parentElement.querySelector('.meme-file').click()">Choose File</button>
                        </div>
                        <div class="file-preview meme-preview" style="display: none;">
                            <img class="preview-image" src="" alt="Meme preview">
                            <div class="preview-info">
                                <span class="preview-name"></span>
                                <span class="preview-size"></span>
                            </div>
                            <button type="button" class="remove-preview-btn meme-remove-btn" title="Remove image">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="file-validation meme-validation"></div>
                </div>
                <div class="form-divider">OR</div>
                <input type="url" class="form-input meme-url" placeholder="https://example.com/meme.jpg">
            </div>
            <button type="button" class="remove-meme-btn" onclick="window.adminPage.removeMemeInput(this)">
                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
            </button>
        `;
        container.appendChild(memeGroup);

        // Setup enhanced file input functionality
        this.setupMemeFileInput(memeGroup, memeId);
    }

    removeMemeInput(button) {
        const memeGroup = button.parentElement;
        const container = document.getElementById('memes-container');

        if (container.children.length > 1) {
            UIManager.showConfirmation(
                'Remove Meme',
                'Are you sure you want to remove this meme?',
                () => {
                    memeGroup.remove();
                }
            );
        } else {
            UIManager.showNotification('At least one meme is required', 'error');
        }
    }

    async handleUpload(e) {
        e.preventDefault();

        UIManager.showLoading('Creating collection...');

        try {
            const title = document.getElementById('series-title')?.value || '';
            const characters = document.getElementById('series-characters')?.value.split(',').map(c => c.trim()) || [];
            const description = document.getElementById('series-description')?.value || '';
            const coverImageUrl = document.getElementById('cover-image')?.value || '';
            const coverImageFile = document.getElementById('cover-image-file')?.files[0];

            // Handle cover image
            let coverImage = coverImageUrl;
            if (coverImageFile) {
                try {
                    const coverMetadata = {
                        title: `${title} - Cover Image`,
                        collection: title,
                        description: `Cover image for ${title} collection`
                    };
                    const coverData = await ServerlessFileManager.uploadImageWithMetadata(coverImageFile, coverMetadata);
                    coverImage = coverData.url;
                } catch (error) {
                    UIManager.showNotification('Error uploading cover image: ' + error.message, 'error');
                    return;
                }
            }

            // Collect memes
            const memeGroups = document.querySelectorAll('.meme-input-group');
            const memes = [];

            for (let i = 0; i < memeGroups.length; i++) {
                const group = memeGroups[i];
                const titleInput = group.querySelector('.meme-title');
                const fileInput = group.querySelector('.meme-file');
                const urlInput = group.querySelector('.meme-url');

                if (!titleInput?.value?.trim()) continue;

                let imageUrl = urlInput?.value?.trim() || '';

                // Upload file if provided
                if (fileInput?.files[0]) {
                    try {
                        const memeMetadata = {
                            title: titleInput.value.trim(),
                            collection: title,
                            description: `Meme from ${title} collection`
                        };
                        const uploadResult = await ServerlessFileManager.uploadImageWithMetadata(fileInput.files[0], memeMetadata);
                        imageUrl = uploadResult.url;
                    } catch (error) {
                        UIManager.showNotification('Error uploading meme image: ' + error.message, 'error');
                    }
                }

                memes.push({
                    id: Utils.generateId(),
                    title: titleInput.value.trim(),
                    imageUrl: imageUrl,
                    description: '',
                    tags: [],
                    createdAt: new Date().toISOString(),
                    order: i + 1
                });
            }

            if (memes.length === 0) {
                throw new Error('Please add at least one meme');
            }

            // Create collection
            const newCollection = {
                title: title.trim(),
                coverImage: coverImage,
                characters: characters.filter(c => c.length > 0),
                releaseDate: new Date().toISOString(),
                description: description.trim(),
                memes: memes
            };

            await ServerlessDataManager.saveCollection(newCollection);

            // Reset form
            const uploadForm = document.getElementById('upload-form');
            if (uploadForm) uploadForm.reset();

            const memesContainer = document.getElementById('memes-container');
            if (memesContainer) memesContainer.innerHTML = '';

            this.addMemeInput();
            await this.renderExistingSeries();

            UIManager.showNotification('Collection created successfully!', 'success');

        } catch (error) {
            console.error('Error creating collection:', error);
            UIManager.showNotification('Error: ' + error.message, 'error');
        } finally {
            UIManager.hideLoading();
        }
    }

    async renderExistingSeries() {
        const container = document.getElementById('admin-series-list');
        if (!container) return;

        try {
            this.data = await ServerlessDataManager.getData();

            if (this.data.series.length === 0) {
                container.innerHTML = '<p style="color: var(--text-secondary); text-align: center; padding: 2rem;">No collections created yet.</p>';
                return;
            }

            container.innerHTML = this.data.series.map(series => `
                <div class="admin-series-card">
                    <h3 class="admin-series-title">${series.title}</h3>
                    <div class="admin-series-meta">
                        ${series.memes?.length || 0} memes • ${Utils.formatDate(series.releaseDate || series.createdAt)}
                    </div>
                    <div class="admin-series-actions">
                        <button class="admin-series-btn" onclick="window.open('/${Utils.slugify(series.title)}', '_blank')">
                            View
                        </button>
                        <button class="admin-series-btn delete" onclick="window.adminPage.deleteCollection('${series.id}')">
                            Delete
                        </button>
                    </div>
                </div>
            `).join('');
        } catch (error) {
            console.error('Error rendering existing series:', error);
            container.innerHTML = '<p style="color: var(--text-secondary); text-align: center; padding: 2rem;">Error loading collections.</p>';
        }
    }

    async deleteCollection(collectionId) {
        const series = this.data.series.find(s => s.id === collectionId);
        const seriesTitle = series ? series.title : 'this collection';

        UIManager.showConfirmation(
            'Delete Collection',
            `Are you sure you want to delete "${seriesTitle}"? This action cannot be undone.`,
            async () => {
                try {
                    UIManager.showLoading('Deleting collection...');
                    await ServerlessDataManager.deleteCollection(collectionId);
                    await this.renderExistingSeries();
                    UIManager.showNotification('Collection deleted successfully', 'success');
                } catch (error) {
                    console.error('Error deleting collection:', error);
                    UIManager.showNotification('Error deleting collection: ' + error.message, 'error');
                } finally {
                    UIManager.hideLoading();
                }
            }
        );
    }

    // File handling methods
    setupCoverFileInput() {
        const fileInput = document.getElementById('cover-image-file');
        const dragZone = document.getElementById('cover-drag-zone');
        const preview = document.getElementById('cover-preview');
        const removeBtn = document.getElementById('cover-remove-btn');
        const validation = document.getElementById('cover-validation');

        if (!fileInput || !dragZone) return;

        // File input change handler
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelect(e.target.files[0], preview, validation, dragZone);
            }
        });

        // Drag and drop handlers
        dragZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dragZone.classList.add('drag-over');
        });

        dragZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dragZone.classList.remove('drag-over');
        });

        dragZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dragZone.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                this.handleFileSelect(files[0], preview, validation, dragZone);
            }
        });

        // Remove button handler
        if (removeBtn) {
            removeBtn.addEventListener('click', () => {
                this.clearFilePreview(fileInput, preview, dragZone);
            });
        }
    }

    setupMemeFileInput(memeGroup, memeId) {
        const fileInput = memeGroup.querySelector('.meme-file');
        const dragZone = memeGroup.querySelector('.meme-drag-zone');
        const preview = memeGroup.querySelector('.meme-preview');
        const removeBtn = memeGroup.querySelector('.meme-remove-btn');
        const validation = memeGroup.querySelector('.meme-validation');

        // File input change handler
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelect(e.target.files[0], preview, validation, dragZone);
            }
        });

        // Drag and drop handlers
        dragZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dragZone.classList.add('drag-over');
        });

        dragZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dragZone.classList.remove('drag-over');
        });

        dragZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dragZone.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                this.handleFileSelect(files[0], preview, validation, dragZone);
            }
        });

        // Remove button handler
        removeBtn.addEventListener('click', () => {
            this.clearFilePreview(fileInput, preview, dragZone);
        });
    }

    handleFileSelect(file, preview, validation, dragZone) {
        // Validate file
        const validationResult = ServerlessFileManager.validateFile(file);

        if (!validationResult.isValid) {
            validation.innerHTML = `<div class="error-message">${validationResult.errors.join('<br>')}</div>`;
            return;
        }

        // Clear validation
        validation.innerHTML = '';

        // Show preview
        const reader = new FileReader();
        reader.onload = (e) => {
            const previewImg = preview.querySelector('.preview-image');
            const previewName = preview.querySelector('.preview-name');
            const previewSize = preview.querySelector('.preview-size');

            if (previewImg) previewImg.src = e.target.result;
            if (previewName) previewName.textContent = file.name;
            if (previewSize) previewSize.textContent = ServerlessFileManager.formatFileSize(file.size);

            dragZone.querySelector('.drag-drop-content').style.display = 'none';
            preview.style.display = 'flex';
        };
        reader.readAsDataURL(file);
    }

    clearFilePreview(fileInput, preview, dragZone) {
        fileInput.value = '';
        preview.style.display = 'none';
        dragZone.querySelector('.drag-drop-content').style.display = 'flex';
    }
}

// Series page functionality with Serverless API
class SeriesPage {
    constructor() {
        this.data = { series: [] };
        this.series = null;
        this.viewMode = 'grid';
        this.refreshInterval = null;
        this.init();
    }

    async init() {
        UIManager.showLoading('Loading collection...');

        try {
            console.log('DEBUG: SeriesPage - Loading data...');
            await this.loadData();
            console.log('DEBUG: SeriesPage - Data loaded:', this.data);

            this.findSeries();
            console.log('DEBUG: SeriesPage - Found series:', this.series);

            if (!this.series) {
                console.log('DEBUG: SeriesPage - No series found, setting default metadata');
                UIManager.showNotification('Collection not found', 'error');
                // Set fallback metadata
                MetadataManager.setDefaultMetadata();
                setTimeout(() => {
                    window.location.href = '/home';
                }, 2000);
                return;
            }

            console.log('DEBUG: SeriesPage - About to set collection metadata');
            // Update metadata IMMEDIATELY after finding series
            MetadataManager.setCollectionMetadata(this.series);

            this.renderSeriesInfo();
            this.renderMemesGrid();
            this.setupEventListeners();
            this.setupModal();

            // Set up periodic refresh
            this.refreshInterval = setInterval(() => {
                this.loadData(true);
            }, 30000);

        } catch (error) {
            console.error('Error initializing series page:', error);
            UIManager.showNotification('Error loading collection', 'error');
        } finally {
            UIManager.hideLoading();
        }
    }

    async loadData(silent = false) {
        try {
            if (!silent) UIManager.showLoading('Loading collection...');

            this.data = await ServerlessDataManager.getData();

            if (!silent) {
                this.findSeries();
                if (this.series) {
                    this.renderSeriesInfo();
                    this.renderMemesGrid();
                }
            }
        } catch (error) {
            console.error('Error loading data:', error);
            if (!silent) {
                UIManager.showNotification('Error loading collection', 'error');
            }
        } finally {
            if (!silent) UIManager.hideLoading();
        }
    }

    findSeries() {
        const path = window.location.pathname;
        const urlParams = new URLSearchParams(window.location.search);

        console.log('DEBUG: findSeries - path:', path, 'urlParams:', urlParams.toString());

        if (urlParams.has('collection')) {
            const collectionSlug = urlParams.get('collection');
            console.log('DEBUG: findSeries - using query param slug:', collectionSlug);
            this.series = Utils.findSeriesBySlug(collectionSlug, this.data);
        } else {
            const slug = path.substring(1).replace(/\/$/, ''); // Remove trailing slash
            console.log('DEBUG: findSeries - using path slug:', slug);
            this.series = Utils.findSeriesBySlug(slug, this.data);
        }

        console.log('DEBUG: findSeries - result:', this.series);
    }

    renderSeriesInfo() {
        const infoContainer = document.getElementById('series-info');
        if (!infoContainer || !this.series) return;

        const imageUrl = this.series.coverImage || Utils.createPlaceholderSVG(300, 400, 'Placeholder');
        const memeCount = this.series.memes?.length || 0;

        infoContainer.innerHTML = `
            <div class="series-details card" style="padding: 2rem; margin-bottom: 2rem;">
                <div style="display: flex; gap: 2rem; align-items: start; flex-wrap: wrap;">
                    <div style="flex-shrink: 0;">
                        <img src="${imageUrl}" alt="${this.series.title}"
                             style="width: 200px; height: 267px; object-fit: cover; border-radius: 0.5rem;">
                    </div>
                    <div style="flex: 1; min-width: 300px;">
                        <h1 style="font-size: 2rem; font-weight: bold; color: var(--text-primary); margin-bottom: 1rem;">
                            ${this.series.title}
                        </h1>
                        <p style="color: var(--text-secondary); margin-bottom: 1.5rem; line-height: 1.6;">
                            ${this.series.description || `A collection of ${memeCount} memes featuring ${(this.series.characters || []).join(', ')}.`}
                        </p>
                        <div style="display: flex; flex-wrap: wrap; gap: 1rem; margin-bottom: 1.5rem;">
                            <div>
                                <span style="color: var(--text-muted); font-size: 0.875rem;">Released:</span>
                                <span style="color: var(--text-primary); margin-left: 0.5rem;">${Utils.formatDate(this.series.releaseDate || this.series.createdAt)}</span>
                            </div>
                            <div>
                                <span style="color: var(--text-muted); font-size: 0.875rem;">Memes:</span>
                                <span style="color: var(--text-primary); margin-left: 0.5rem;">${memeCount}</span>
                            </div>
                        </div>
                        <div>
                            <span style="color: var(--text-muted); font-size: 0.875rem; margin-bottom: 0.5rem; display: block;">Characters:</span>
                            <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                ${(this.series.characters || []).map(character =>
                                    `<span class="character-tag">${character}</span>`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderMemesGrid() {
        const grid = document.getElementById('memes-grid');
        if (!grid || !this.series) return;

        const memes = this.series.memes || [];

        if (memes.length === 0) {
            grid.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">No memes in this collection yet.</p>';
            return;
        }

        grid.innerHTML = memes
            .sort((a, b) => (a.order || 0) - (b.order || 0))
            .map(meme => this.createMemeCard(meme)).join('');
    }

    createMemeCard(meme) {
        // Use placeholder if no image URL or if image URL is empty
        const imageUrl = (meme.imageUrl && meme.imageUrl.trim() !== '')
            ? meme.imageUrl
            : Utils.createPlaceholderSVG(400, 400, meme.title);
        const collectionSlug = Utils.slugify(this.series.title);
        const memeSlug = Utils.slugify(meme.title);

        return `
            <a href="/${collectionSlug}/${memeSlug}" class="meme-card card" data-meme-id="${meme.id}">
                <div class="meme-image-container">
                    <img src="${imageUrl}" alt="${meme.title}" class="meme-image" loading="lazy"
                         onerror="this.src='${Utils.createPlaceholderSVG(400, 400, meme.title)}'">
                    <div class="meme-overlay">
                        <div class="meme-play-btn">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12l-3-3m0 6l3-3m-3-3v6" />
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="meme-info">
                    <h3 class="meme-title">${meme.title}</h3>
                    ${meme.description ? `<p class="meme-description">${meme.description}</p>` : ''}
                </div>
            </a>
        `;
    }

    setupEventListeners() {
        // Clean up interval when page unloads
        window.addEventListener('beforeunload', () => {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
        });

        // Visibility change - refresh when tab becomes visible
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.loadData(true);
            }
        });
    }

    setupModal() {
        const modal = document.getElementById('meme-modal');
        const closeBtn = document.getElementById('close-modal');

        if (closeBtn) closeBtn.addEventListener('click', () => this.closeMemeModal());

        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeMemeModal();
                }
            });
        }

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal && modal.style.display !== 'none') {
                this.closeMemeModal();
            }
        });
    }

    openMemeModal(memeId) {
        const meme = (this.series.memes || []).find(m => m.id === memeId);
        if (!meme) return;

        this.currentMeme = meme;
        const modal = document.getElementById('meme-modal');
        const title = document.getElementById('modal-title');
        const description = document.getElementById('modal-description');
        const image = document.getElementById('modal-image');
        const date = document.getElementById('modal-date');

        if (title) title.textContent = meme.title;
        if (description) {
            description.textContent = meme.description || '';
            description.style.display = meme.description ? 'block' : 'none';
        }
        if (date) date.textContent = Utils.formatDate(meme.createdAt);

        if (modal) {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        if (image) {
            const imageUrl = meme.imageUrl || Utils.createPlaceholderSVG(800, 600, meme.title);
            image.src = imageUrl;
        }
    }

    closeMemeModal() {
        const modal = document.getElementById('meme-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }
        this.currentMeme = null;
    }

    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

// Meme page functionality with Serverless API
class MemePage {
    constructor() {
        this.data = { series: [] };
        this.series = null;
        this.meme = null;
        this.currentIndex = 0;
        this.init();
    }

    async init() {
        UIManager.showLoading('Loading meme...');

        try {
            console.log('DEBUG: MemePage - Loading data...');
            this.data = await ServerlessDataManager.getData();
            console.log('DEBUG: MemePage - Data loaded:', this.data);

            this.findMemeAndSeries();
            console.log('DEBUG: MemePage - Found series:', this.series);
            console.log('DEBUG: MemePage - Found meme:', this.meme);

            if (!this.series || !this.meme) {
                console.log('DEBUG: MemePage - Missing series or meme, setting default metadata');
                if (!this.series) {
                    UIManager.showNotification('Collection not found', 'error');
                } else {
                    UIManager.showNotification('Meme not found', 'error');
                }
                // Set fallback metadata
                MetadataManager.setDefaultMetadata();
                setTimeout(() => {
                    window.location.href = '/home';
                }, 2000);
                return;
            }

            console.log('DEBUG: MemePage - About to set meme metadata');
            // Update metadata IMMEDIATELY after finding meme and series
            MetadataManager.setMemeMetadata(this.meme, this.series);

            this.currentIndex = (this.series.memes || []).findIndex(m => m.id === this.meme.id);
            this.renderMeme();
            this.setupEventListeners();

        } catch (error) {
            console.error('Error initializing meme page:', error);
            UIManager.showNotification('Error loading meme', 'error');
        } finally {
            UIManager.hideLoading();
        }
    }

    findMemeAndSeries() {
        const urlParams = new URLSearchParams(window.location.search);
        const path = window.location.pathname;

        if (urlParams.has('collection') && urlParams.has('meme')) {
            const collectionSlug = urlParams.get('collection');
            const memeSlug = urlParams.get('meme');
            this.series = Utils.findSeriesBySlug(collectionSlug, this.data);
            this.meme = this.series ? Utils.findMemeBySlug(memeSlug, this.series) : null;
        } else {
            const parts = path.substring(1).split('/').filter(part => part.length > 0);
            if (parts.length === 2) {
                const [collectionSlug, memeSlug] = parts;
                this.series = Utils.findSeriesBySlug(collectionSlug, this.data);
                this.meme = this.series ? Utils.findMemeBySlug(memeSlug, this.series) : null;
            }
        }
    }

    renderMeme() {
        document.title = `${this.meme.title} - ${this.series.title} - MemesByKayé`;

        const elements = {
            title: document.getElementById('meme-title'),
            image: document.getElementById('meme-image'),
            collection: document.getElementById('meme-collection'),
            date: document.getElementById('meme-date'),
            counter: document.getElementById('meme-counter'),
            backToCollection: document.getElementById('back-to-collection'),
            collectionBreadcrumb: document.getElementById('collection-breadcrumb'),
            memeBreadcrumb: document.getElementById('meme-breadcrumb')
        };

        if (elements.title) elements.title.textContent = this.meme.title;
        if (elements.image) {
            // Use placeholder if no image URL or if image URL is empty
            const imageUrl = (this.meme.imageUrl && this.meme.imageUrl.trim() !== '')
                ? this.meme.imageUrl
                : Utils.createPlaceholderSVG(800, 600, this.meme.title);
            elements.image.src = imageUrl;
            elements.image.alt = this.meme.title;
            // Add error handling for broken images
            elements.image.onerror = () => {
                elements.image.src = Utils.createPlaceholderSVG(800, 600, this.meme.title);
            };
        }
        if (elements.collection) elements.collection.textContent = this.series.title;
        if (elements.date) elements.date.textContent = Utils.formatDate(this.meme.createdAt);
        if (elements.counter) elements.counter.textContent = `${this.currentIndex + 1} of ${this.series.memes?.length || 0}`;

        const collectionSlug = Utils.slugify(this.series.title);
        if (elements.backToCollection) elements.backToCollection.href = `/${collectionSlug}`;
        if (elements.collectionBreadcrumb) {
            elements.collectionBreadcrumb.href = `/${collectionSlug}`;
            elements.collectionBreadcrumb.textContent = this.series.title;
        }
        if (elements.memeBreadcrumb) elements.memeBreadcrumb.textContent = this.meme.title;
    }

    setupEventListeners() {
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');

        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.navigateMeme(-1));
            prevBtn.disabled = this.currentIndex === 0;
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.navigateMeme(1));
            nextBtn.disabled = this.currentIndex === (this.series.memes?.length || 1) - 1;
        }

        const shareBtn = document.getElementById('share-btn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.shareMeme());
        }

        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => this.openFullscreen());
        }

        const fullscreenClose = document.getElementById('fullscreen-close');
        if (fullscreenClose) {
            fullscreenClose.addEventListener('click', () => this.closeFullscreen());
        }

        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') this.navigateMeme(-1);
            if (e.key === 'ArrowRight') this.navigateMeme(1);
            if (e.key === 'Escape') this.closeFullscreen();
        });
    }

    navigateMeme(direction) {
        const memes = this.series.memes || [];
        const newIndex = this.currentIndex + direction;

        if (newIndex >= 0 && newIndex < memes.length) {
            const newMeme = memes[newIndex];
            const collectionSlug = Utils.slugify(this.series.title);
            const memeSlug = Utils.slugify(newMeme.title);

            // Update metadata before navigation for better social sharing
            MetadataManager.setMemeMetadata(newMeme, this.series);

            window.location.href = `/${collectionSlug}/${memeSlug}`;
        }
    }

    async shareMeme() {
        const shareData = {
            title: this.meme.title,
            text: `Check out this meme: ${this.meme.title}`,
            url: window.location.href
        };

        if (navigator.share) {
            try {
                await navigator.share(shareData);
            } catch (err) {
                console.log('Error sharing:', err);
            }
        } else {
            try {
                await navigator.clipboard.writeText(window.location.href);
                UIManager.showNotification('Link copied to clipboard!', 'success');
            } catch (err) {
                console.log('Error copying to clipboard:', err);
            }
        }
    }

    openFullscreen() {
        const modal = document.getElementById('fullscreen-modal');
        const fullscreenImage = document.getElementById('fullscreen-image');

        if (modal && fullscreenImage && this.meme) {
            const imageUrl = (this.meme.imageUrl && this.meme.imageUrl.trim() !== '')
                ? this.meme.imageUrl
                : Utils.createPlaceholderSVG(800, 600, this.meme.title);

            fullscreenImage.src = imageUrl;
            fullscreenImage.alt = this.meme.title;
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Add click-outside-to-close functionality
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeFullscreen();
                }
            });
        }
    }

    closeFullscreen() {
        const modal = document.getElementById('fullscreen-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    }
}

// Global error handling
window.addEventListener('error', (e) => {
    console.error('Global error:', e.error);
});

// Export for global access
window.ServerlessDataManager = ServerlessDataManager;
window.ServerlessFileManager = ServerlessFileManager;
window.Utils = Utils;
