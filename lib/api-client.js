// API Client for GitHub + Vercel + Discord CDN Backend
// Replaces Firebase with serverless functions

class APIClient {
    constructor() {
        // Detect environment without using process.env
        this.baseURL = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
            ? 'http://localhost:3000'
            : window.location.origin;
        // Password will be validated through API, not stored here
    }

    // Authenticate admin password
    async authenticateAdmin(password) {
        try {
            // Trim password to avoid whitespace issues
            const trimmedPassword = password?.trim();
            if (!trimmedPassword) {
                return { success: false, error: 'Password is required' };
            }

            const response = await fetch(`${this.baseURL}/api/auth`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ password: trimmedPassword }),
            });

            const result = await response.json();

            if (response.ok && result.success) {
                // Store the validated password for API calls
                this.validatedPassword = trimmedPassword;
                return { success: true };
            } else {
                return { success: false, error: result.error || 'Authentication failed' };
            }
        } catch (error) {
            console.error('Error authenticating:', error);
            return { success: false, error: 'Network error' };
        }
    }

    // Get all collections
    async getCollections() {
        try {
            const response = await fetch(`${this.baseURL}/api/collections`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error fetching collections:', error);
            return { series: [] }; // Return empty data on error
        }
    }

    // Create new collection
    async createCollection(collectionData) {
        try {
            // Ensure we have a valid password
            if (!this.validatedPassword) {
                throw new Error('Authentication required. Please log in again.');
            }

            const response = await fetch(`${this.baseURL}/api/collections`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    collection: collectionData,
                    adminPassword: this.validatedPassword
                }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || `HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error creating collection:', error);
            throw error;
        }
    }

    // Update existing collection
    async updateCollection(collectionId, updates) {
        try {
            const response = await fetch(`${this.baseURL}/api/collections`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    collectionId,
                    updates,
                    adminPassword: this.validatedPassword
                }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || `HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error updating collection:', error);
            throw error;
        }
    }

    // Delete collection
    async deleteCollection(collectionId) {
        try {
            // Ensure we have a valid password
            if (!this.validatedPassword) {
                throw new Error('Authentication required. Please log in again.');
            }

            const response = await fetch(`${this.baseURL}/api/collections`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    collectionId,
                    adminPassword: this.validatedPassword
                }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || `HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error deleting collection:', error);
            throw error;
        }
    }

    // Upload image to Discord CDN
    async uploadImage(file) {
        try {
            // Ensure we have a valid password
            if (!this.validatedPassword) {
                throw new Error('Authentication required. Please log in again.');
            }

            // Convert file to base64
            const base64Data = await this.fileToBase64(file);

            const response = await fetch(`${this.baseURL}/api/upload`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    imageData: base64Data,
                    fileName: `${Date.now()}_${file.name}`,
                    adminPassword: this.validatedPassword
                }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || `HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            return {
                url: result.url,
                name: result.fileName,
                size: result.size,
                type: file.type
            };
        } catch (error) {
            console.error('Error uploading image:', error);
            throw error;
        }
    }

    // Helper: Convert file to base64
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    // Validate file before upload
    validateFile(file) {
        const errors = [];
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        if (!allowedTypes.includes(file.type)) {
            errors.push('Invalid file type. Please use JPEG, PNG, GIF, or WebP.');
        }

        if (file.size > maxSize) {
            errors.push(`File too large. Maximum size is ${this.formatFileSize(maxSize)}.`);
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Format file size for display
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Compress image if needed
    async compressImage(file, maxWidth = 800, maxHeight = 600, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate new dimensions
                let { width, height } = img;

                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }

                canvas.width = width;
                canvas.height = height;

                // Draw and compress
                ctx.drawImage(img, 0, 0, width, height);
                canvas.toBlob(resolve, file.type, quality);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    // Upload with compression
    async uploadImageWithCompression(file) {
        try {
            // Validate file first
            const validation = this.validateFile(file);
            if (!validation.isValid) {
                throw new Error(validation.errors.join(' '));
            }

            // Compress large images
            let processedFile = file;
            if (file.size > 1024 * 1024) { // 1MB
                processedFile = await this.compressImage(file);
            }

            return await this.uploadImage(processedFile);
        } catch (error) {
            console.error('Error uploading with compression:', error);
            throw error;
        }
    }
}

// Make the class available globally first
window.APIClient = APIClient;

// Create global instance
try {
    window.apiClient = new APIClient();
} catch (error) {
    console.error('Error creating API client:', error);
    // Will be created later by the main script
}
