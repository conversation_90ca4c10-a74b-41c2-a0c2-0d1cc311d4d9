# MemesByKayé - Serverless Edition

A personal meme collection website powered by a completely free serverless stack. Built with vanilla HTML, CSS, and JavaScript, featuring a modern professional design and unlimited global storage.

## 🚀 **100% Free Serverless Stack**

- ✅ **GitHub** → Database (JSON files)
- ✅ **Vercel** → Serverless API functions
- ✅ **Discord CDN** → Unlimited image storage
- ✅ **$0 monthly cost** → Forever free!

## ✨ Features

### 🎨 **Professional Design**
- Modern dark theme with electric accent colors
- Glassmorphism effects and smooth animations
- Responsive mobile-first design
- Professional typography using Inter font

### 📚 **Collection Management**
- Organize memes into collections with metadata
- Cover images, descriptions, and character tags
- Real-time updates across all devices
- Clean SEO-friendly URLs

### 🔐 **Admin Panel**
- Password-protected admin interface
- Drag-and-drop file uploads with compression
- Form validation and error handling
- Draft saving functionality

### 🌍 **Global Access**
- **Your followers can see your memes** → No more localStorage!
- **Real-time updates** → Changes appear in ~30 seconds
- **Unlimited storage** → Discord CDN has no limits
- **Fast worldwide** → Discord's global CDN network

## 🏗️ Architecture

### **Serverless Backend**
```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   Frontend  │───▶│ Vercel API   │───▶│   GitHub    │
│  (Browser)  │    │ Functions    │    │ (Database)  │
└─────────────┘    └──────────────┘    └─────────────┘
                           │
                           ▼
                   ┌──────────────┐
                   │ Discord CDN  │
                   │ (Images)     │
                   └──────────────┘
```

### **File Structure**
```
MemesByK/
├── api/                    # Serverless functions
│   ├── collections.js      # CRUD operations on GitHub JSON
│   └── upload.js          # Upload images to Discord CDN
├── lib/
│   └── api-client.js      # Frontend API wrapper
├── data/
│   └── collections.json   # Your meme database
├── index.html             # Homepage
├── admin.html             # Admin panel
├── series.html            # Collection viewing
├── meme.html              # Individual meme page
├── styles.css             # Design system
├── script-serverless.js   # Application logic
├── package.json           # Dependencies
├── vercel.json            # Deployment config
└── SERVERLESS_SETUP.md    # Setup guide
```

## 🚀 Quick Start

### 1. **Setup (15 minutes)**
Follow the detailed guide in [`SERVERLESS_SETUP.md`](SERVERLESS_SETUP.md):

1. **GitHub Token** → Create personal access token
2. **Discord Webhook** → Create webhook in any Discord server
3. **Vercel Environment Variables** → Add 6 environment variables
4. **Deploy** → Push to GitHub, auto-deploys

### 2. **Test Your Setup**
1. Visit your deployed website
2. Go to `/admin` and login with `Kuku1010@`
3. Create a test collection with an image
4. Verify it appears on homepage and in GitHub

### 3. **Share with Followers**
Your meme website is now globally accessible! 🎉

## 🔧 How It Works

### **Data Flow**
1. **You upload meme** → Vercel API function
2. **Image goes to Discord** → Gets CDN URL
3. **Data goes to GitHub** → Updates JSON file
4. **GitHub triggers redeploy** → Site updates globally
5. **Followers see changes** → Within ~30 seconds

### **API Endpoints**
- `GET /api/collections` → Fetch all collections
- `POST /api/collections` → Create new collection
- `PUT /api/collections` → Update collection
- `DELETE /api/collections` → Delete collection
- `POST /api/upload` → Upload image to Discord CDN

## 🛡️ Security

- ✅ **Admin password protection** → Only you can upload
- ✅ **File type validation** → Only images allowed
- ✅ **File size limits** → 5MB maximum
- ✅ **CORS protection** → Prevents abuse
- ✅ **GitHub token security** → Server-side only

## 🎯 Why This Stack?

### **vs Firebase**
- ❌ Firebase: Requires credit card, billing alerts
- ✅ Serverless: 100% free, no surprises

### **vs localStorage**
- ❌ localStorage: Only you can see data
- ✅ Serverless: Global access, real followers

### **vs Other Solutions**
- 🌍 **Truly unlimited** → Discord CDN has no storage limits
- ⚡ **Super fast** → Discord's global CDN network
- 🔒 **Secure** → Admin password protection
- 📱 **Mobile ready** → Works on all devices

## 🛠️ Development

### **Local Development**
```bash
# Install dependencies
npm install

# Run locally
npm run dev

# Deploy
npm run deploy
```

### **Environment Variables**
```env
GITHUB_TOKEN=ghp_your_token_here
GITHUB_OWNER=your-username
GITHUB_REPO=MemesByK
ADMIN_PASSWORD=Kuku1010@
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/...
NODE_ENV=production
```

### **Adding Features**
- **New pages**: Add HTML file and update `vercel.json`
- **New API endpoints**: Create in `/api` folder
- **Frontend changes**: Update `script-serverless.js`

## 📱 Browser Support

- Chrome/Edge 88+
- Firefox 85+
- Safari 14+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🐛 Troubleshooting

**Common Issues:**
- **"GitHub API failed"** → Check `GITHUB_TOKEN` permissions
- **"Discord upload failed"** → Verify webhook URL is active
- **"Unauthorized"** → Check `ADMIN_PASSWORD` environment variable
- **Images not loading** → Discord CDN URLs should start with `https://cdn.discordapp.com/`

See [`SERVERLESS_SETUP.md`](SERVERLESS_SETUP.md) for detailed troubleshooting.

## 📄 License

MIT License - Free for personal and commercial use.

## 🎉 Credits

Built with ❤️ using the power of free serverless technologies. No monthly fees, no credit cards, no limits!
